"""
数据传输对象(DTO) - 用于接口层和服务层之间的数据传递
按业务逻辑分类：翻译类、翻译记录类、翻译任务类、文件上传类
"""
from dataclasses import dataclass
from typing import List, Dict, Any, Optional
from datetime import datetime


# ==================== 文件上传类 DTO ====================
@dataclass
class FileUploadRequest:
    """
    通用文件上传请求DTO
    不对文件类型做任何限制，具体限制由业务服务层处理
    """
    filename: str  # 安全处理后的文件名
    file_content: bytes  # 文件内容字节流
    original_filename: str  # 原始文件名
    file_size: Optional[int] = None  # 文件大小（字节）
    content_type: Optional[str] = None  # MIME类型


@dataclass
class FileProcessResult:
    """文件处理结果DTO"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    error_details: Optional[Dict[str, Any]] = None


# ==================== 翻译类 DTO ====================
@dataclass
class TranslationItem:
    """单个翻译项DTO"""
    id: int
    text: str
    target_languages: List[str]
    source_language: Optional[str] = "auto"  # 源语言，默认自动检测


@dataclass
class TranslationConfig:
    """翻译配置DTO"""
    style: str = "formal"  # 翻译风格：formal, informal, casual
    domain: str = "general"  # 翻译领域：general, technical, business
    preserve_formatting: bool = True  # 是否保持格式
    custom_glossary: Optional[Dict[str, str]] = None  # 自定义词汇表


@dataclass
class BatchTranslateRequest:
    """批量翻译请求DTO"""
    translation_data: List[Dict[str, Any]]  # 原始翻译数据
    config: Dict[str, Any]  # 原始配置数据

    def to_translation_items(self) -> List[TranslationItem]:
        """转换为翻译项列表"""
        items = []
        for item_data in self.translation_data:
            item = TranslationItem(
                id=item_data['id'],
                text=item_data['text'],
                target_languages=item_data['target_languages'],
                source_language=item_data.get('source_language', 'auto')
            )
            items.append(item)
        return items

    def to_translation_config(self) -> TranslationConfig:
        """转换为翻译配置"""
        return TranslationConfig(
            style=self.config.get('style', 'formal'),
            domain=self.config.get('domain', 'general'),
            preserve_formatting=self.config.get('preserve_formatting', True),
            custom_glossary=self.config.get('custom_glossary')
        )


@dataclass
class SingleTranslateRequest:
    """单条翻译请求DTO"""
    text: str
    target_language: str
    source_language: str = "auto"
    config: Optional[TranslationConfig] = None


# ==================== 翻译记录类 DTO ====================
@dataclass
class TranslationRecordRequest:
    """翻译记录请求DTO"""
    task_id: str
    source_id: int
    source_text: str
    target_language: str
    source_language: str = "auto"


@dataclass
class TranslationRecordResponse:
    """翻译记录响应DTO"""
    id: int
    task_id: str
    source_id: int
    source_text: str
    target_language: str
    translated_text: Optional[str]
    status: str  # pending, processing, completed, failed
    error_message: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


@dataclass
class TranslationRecordFilter:
    """翻译记录过滤条件DTO"""
    task_id: Optional[str] = None
    status: Optional[str] = None
    target_language: Optional[str] = None
    limit: int = 50
    offset: int = 0


# ==================== 翻译任务类 DTO ====================
@dataclass
class TranslationTaskRequest:
    """翻译任务创建请求DTO"""
    total_records: int
    config: Dict[str, Any]
    description: Optional[str] = None


@dataclass
class TranslationTaskResponse:
    """翻译任务响应DTO"""
    id: str
    status: str  # pending, processing, completed, failed, cancelled
    total_records: int
    completed_records: int = 0
    failed_records: int = 0
    progress_percentage: float = 0.0
    config: Dict[str, Any]
    description: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    estimated_completion: Optional[datetime] = None


@dataclass
class TranslationTaskFilter:
    """翻译任务过滤条件DTO"""
    status: Optional[str] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    limit: int = 50
    offset: int = 0


@dataclass
class TaskStatusUpdate:
    """任务状态更新DTO"""
    task_id: str
    status: str
    completed_records: Optional[int] = None
    failed_records: Optional[int] = None
    error_message: Optional[str] = None


# ==================== 通用请求类 DTO ====================
@dataclass
class PaginationRequest:
    """分页请求DTO"""
    limit: int = 50
    offset: int = 0

    def __post_init__(self):
        """验证分页参数"""
        self.limit = min(max(self.limit, 1), 100)  # 限制在1-100之间
        self.offset = max(self.offset, 0)  # 不能小于0


@dataclass
class TaskIdRequest:
    """任务ID请求DTO"""
    task_id: str

    def __post_init__(self):
        """验证任务ID"""
        if not self.task_id or not self.task_id.strip():
            raise ValueError("任务ID不能为空")
        self.task_id = self.task_id.strip()


@dataclass
class SearchRequest:
    """搜索请求DTO"""
    query: str
    filters: Optional[Dict[str, Any]] = None
    sort_by: Optional[str] = None
    sort_order: str = "desc"  # asc, desc
    pagination: Optional[PaginationRequest] = None


# ==================== 响应基类 DTO ====================
@dataclass
class BaseResponse:
    """基础响应DTO"""
    success: bool
    message: str
    code: int = 0
    timestamp: Optional[datetime] = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class DataResponse(BaseResponse):
    """带数据的响应DTO"""
    data: Optional[Any] = None


@dataclass
class ListResponse(BaseResponse):
    """列表响应DTO"""
    data: List[Any]
    total: int
    pagination: Optional[Dict[str, int]] = None


# 响应DTO可以复用现有的模型类
# 如 BatchTranslateResponse, TranslationHistoryResponse 等