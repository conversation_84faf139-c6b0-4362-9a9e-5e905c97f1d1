/**
 * 翻译模块API导出
 */

// 导出类型定义
export type {
  Response,
  ExcelUploadRequest,
  ExcelMetadata,
  ExcelData,
  ExcelUploadResponse,
  TranslationConfig,
  TranslationItem,
  BatchTranslateRequest,
  BatchTranslateResponse,
  TranslationTask,
  TranslationHistoryResponse,
  TranslationResultItem,
  TranslationResult,
  TranslationResultsResponse,
  TranslateRequest,
  TranslateResponse
} from './types'

// 导出枚举
export {
  ApiStatus,
  TranslationStatus,
  TranslationStyle,
  TranslationDomain
} from './types'

// 导出服务实例
export { translateApiService, default as translateApi } from './service'