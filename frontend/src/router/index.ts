import { createRouter, createWebHistory } from 'vue-router'
import Prototype from '@/views/Prototype.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: Prototype
    },
    {
      path: '/legacy',
      name: 'legacy',
      component: () => import('@/views/Home.vue')
    },
    {
      path: '/history',
      name: 'history',
      component: () => import('@/views/History.vue')
    }
  ]
})

export default router