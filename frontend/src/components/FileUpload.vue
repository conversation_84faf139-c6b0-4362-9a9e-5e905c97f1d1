<template>
  <div class="file-upload-container">
    <div
      class="upload-area"
      :class="{ 'dragover': isDragOver }"
      @drop="handleDrop"
      @dragover.prevent="handleDragOver"
      @dragenter.prevent="handleDragEnter"
      @dragleave.prevent="handleDragLeave"
    >
      <el-upload
        ref="uploadRef"
        :auto-upload="false"
        :accept="allowedExtensions"
        :on-change="handleFileChange"
        :show-file-list="false"
        :disabled="uploading"
        drag
      >
        <div class="upload-content">
          <el-icon :size="64" class="upload-icon text-gray-400 mb-4">
            <UploadFilled />
          </el-icon>
          <div class="text-xl font-semibold text-gray-700 mb-2">
            拖拽Excel文件到此处
          </div>
          <div class="text-gray-500 mb-4">
            或 <span class="text-blue-600 font-medium">点击选择文件</span>
          </div>
          <div class="text-sm text-gray-400">
            支持格式: .xlsx, .xls | 最大大小: {{ maxFileSize }}
          </div>
        </div>
      </el-upload>
    </div>

    <!-- 文件信息显示 -->
    <div v-if="selectedFile && !uploading" class="file-info mt-6">
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <el-icon :size="24" class="text-blue-500 mr-3">
              <Document />
            </el-icon>
            <div>
              <div class="font-medium text-gray-900">{{ selectedFile.name }}</div>
              <div class="text-sm text-gray-500">{{ formatFileSize(selectedFile.size) }}</div>
            </div>
          </div>
          <div class="flex space-x-2">
            <el-button
              type="primary"
              :loading="uploading"
              @click="handleUpload"
            >
              开始翻译
            </el-button>
            <el-button @click="clearSelectedFile">
              取消
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 上传中状态 -->
    <div v-if="uploading" class="upload-loading mt-6">
      <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div class="flex items-center">
          <el-icon :size="24" class="text-yellow-500 mr-3 animate-spin">
            <Loading />
          </el-icon>
          <div>
            <div class="font-medium text-gray-900">正在翻译文件...</div>
            <div class="text-sm text-gray-500">请稍候，正在处理您的文件</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElUpload, ElIcon, ElButton, ElMessage } from 'element-plus'
import { UploadFilled, Document, Loading } from '@element-plus/icons-vue'
import type { UploadFile } from 'element-plus'

import { useTranslationStore } from '@/stores/translation'
import { translateApiService } from '@/api/translate'
import wsService from '@/services/websocket'

// Props
interface Props {
  maxFileSize?: string
  allowedExtensions?: string
}

const props = withDefaults(defineProps<Props>(), {
  maxFileSize: '50MB',
  allowedExtensions: '.xlsx,.xls'
})

// Emits
const emit = defineEmits<{
  fileSelected: [file: File]
  uploadError: [error: string]
}>()

// Store
const store = useTranslationStore()

// 响应式数据
const uploadRef = ref()
const selectedFile = ref<File | null>(null)
const uploading = ref(false)
const isDragOver = ref(false)

// 计算属性
const maxFileSizeBytes = computed(() => {
  const size = props.maxFileSize.toLowerCase()
  if (size.includes('mb')) {
    return parseInt(size) * 1024 * 1024
  }
  if (size.includes('kb')) {
    return parseInt(size) * 1024
  }
  return parseInt(size)
})

// 拖拽处理
const handleDragOver = (e: DragEvent) => {
  e.preventDefault()
  isDragOver.value = true
}

const handleDragEnter = (e: DragEvent) => {
  e.preventDefault()
  isDragOver.value = true
}

const handleDragLeave = (e: DragEvent) => {
  e.preventDefault()
  isDragOver.value = false
}

const handleDrop = (e: DragEvent) => {
  e.preventDefault()
  isDragOver.value = false

  const files = e.dataTransfer?.files
  if (files && files.length > 0) {
    validateAndSelectFile(files[0])
  }
}

// 文件选择处理
const handleFileChange = (uploadFile: UploadFile) => {
  if (uploadFile.raw) {
    validateAndSelectFile(uploadFile.raw)
  }
}

// 验证并选择文件
const validateAndSelectFile = (file: File) => {
  // 验证文件格式
  const allowedTypes = props.allowedExtensions.split(',').map(ext => ext.trim())
  const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase()

  if (!allowedTypes.includes(fileExtension)) {
    ElMessage.error(`不支持的文件格式，请选择 ${props.allowedExtensions} 格式的文件`)
    return
  }

  // 验证文件大小
  if (file.size > maxFileSizeBytes.value) {
    ElMessage.error(`文件大小超过限制，最大支持 ${props.maxFileSize}`)
    return
  }

  selectedFile.value = file
  emit('fileSelected', file)
}

// 上传文件
const handleUpload = async () => {
  if (!selectedFile.value) return

  try {
    uploading.value = true
    store.setUploadStatus('uploading')

    // 翻译文件
    const response = await translateApiService.uploadFile(selectedFile.value)

    if (response.code === 200) {
      ElMessage.success('开始翻译处理...')

      // 设置任务信息
      store.setCurrentTask({
        taskId: response.data.taskId,
        originalFilename: selectedFile.value.name,
        status: 'processing',
        progress: 0,
        message: '开始翻译处理...',
        createdTime: new Date().toISOString()
      })

      // 连接WebSocket监听进度
      await wsService.connect(response.data.taskId, (progress) => {
        store.updateProgress(progress)
      })

      store.setUploadStatus('processing')
    } else {
      throw new Error(response.message || '翻译失败')
    }
  } catch (error) {
    console.error('Translation failed:', error)
    const errorMessage = error instanceof Error ? error.message : '翻译失败'
    ElMessage.error(errorMessage)
    emit('uploadError', errorMessage)
  } finally {
    uploading.value = false
  }
}

// 清除选中的文件
const clearSelectedFile = () => {
  selectedFile.value = null
  uploadRef.value?.clearFiles()
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
</script>

<style scoped>
.upload-area {
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-content {
  text-align: center;
  padding: 2rem;
}

.upload-icon {
  display: block;
  margin: 0 auto;
}

.file-info {
  animation: fadeIn 0.3s ease-in-out;
}

.upload-loading {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>