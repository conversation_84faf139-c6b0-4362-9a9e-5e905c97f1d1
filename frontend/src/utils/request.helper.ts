import axios, { AxiosError } from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'

/**
 * 创建axios实例
 */
const createAxiosInstance = (): AxiosInstance => {
  const instance = axios.create({
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json'
    }
  })

  return instance
}

/**
 * 统一的请求封装
 * 处理API响应，根据code判断成功或失败
 * code为0时返回data，否则抛出错误
 */
class RequestHelper {
  private instance: AxiosInstance

  constructor() {
    this.instance = createAxiosInstance()
    this.setupInterceptors()
  }

  /**
   * 设置拦截器
   */
  private setupInterceptors(): void {
    // 响应拦截器
    this.instance.interceptors.response.use(
      (response: AxiosResponse) => {
        // HTTP状态码在200-300范围内
        const { data } = response

        // 如果是Blob类型（文件下载），直接返回
        if (data instanceof Blob) {
          return data
        }

        // 检查是否是标准的API响应格式
        if (typeof data === 'object' && data !== null && 'code' in data) {
          // 检查业务状态码
          if (data.code === 0) {
            // 成功，直接返回data字段的内容
            return data.data
          } else {
            // 业务失败，显示错误消息并抛出错误
            const errorMessage = data.message || '请求失败'
            ElMessage.error(errorMessage)

            const error = new Error(errorMessage)
              ; (error as any).code = data.code
              ; (error as any).response = data
            throw error
          }
        }

        // 非标准格式，直接返回原始数据
        return data
      },
      (error: AxiosError) => {
        // HTTP状态码不在200-300范围内
        if (error.response) {
          // 服务器返回了错误状态码
          const message = error.response.statusText || `请求失败: ${error.response.status}`
          const requestError = new Error(message)
            ; (requestError as any).status = error.response.status
            ; (requestError as any).response = error.response.data
          throw requestError
        } else if (error.request) {
          // 请求已发出但没有收到响应
          const errorMessage = '网络错误，请检查网络连接'
          ElMessage.error(errorMessage)
          throw new Error(errorMessage)
        } else {
          // 其他错误
          const errorMessage = error.message || '请求配置错误'
          ElMessage.error(errorMessage)
          throw new Error(errorMessage)
        }
      }
    )
  }

  /**
   * GET请求
   */
  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.instance.get(url, config)
  }

  /**
   * POST请求
   */
  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.instance.post(url, data, config)
  }

  /**
   * PUT请求
   */
  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.instance.put(url, data, config)
  }

  /**
   * DELETE请求
   */
  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.instance.delete(url, config)
  }

  /**
   * PATCH请求
   */
  async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.instance.patch(url, data, config)
  }

  /**
   * 获取axios实例（用于特殊需求）
   */
  getInstance(): AxiosInstance {
    return this.instance
  }
}

// 导出单例实例
export const request = new RequestHelper()
export default request