/**
 * API服务统一入口 - 兼容性层
 * 为了保持向后兼容，这里重新导出翻译API服务
 */
import { translateApiService } from '@/api/translate'
import type { BatchTranslateRequest } from '@/api/translate/types'

// 为了保持向后兼容，创建一个包装类
class ApiService {
  // 上传并解析Excel文件
  async uploadFile(file: File) {
    return translateApiService.uploadFile(file)
  }

  // 翻译文件 - 保留兼容性
  async translateFile(file: File) {
    return this.uploadFile(file)
  }



  // 启动批量翻译任务
  async startBatchTranslation(request: BatchTranslateRequest) {
    return translateApiService.startBatchTranslation(request)
  }

  // 获取翻译历史
  async getHistory(limit?: number, offset?: number) {
    return translateApiService.getHistory(limit, offset)
  }

  // 获取翻译结果
  async getTranslationResults(taskId: string) {
    return translateApiService.getTranslationResults(taskId)
  }

  // 下载翻译结果文件
  async downloadFile(taskId: string) {
    return translateApiService.downloadFile(taskId)
  }

  // 删除翻译任务
  async deleteTask(taskId: string) {
    return translateApiService.deleteTask(taskId)
  }
}

export const apiService = new ApiService()
export default apiService
