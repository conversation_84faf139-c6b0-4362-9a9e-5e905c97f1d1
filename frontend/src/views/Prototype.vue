<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 overflow-y-auto">
    <!-- 导航栏 -->
    <nav class="bg-white/80 backdrop-blur-md shadow-sm border-b border-white/20 sticky top-0 z-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <div class="flex items-center space-x-3">
            <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
              <font-awesome-icon icon="language" class="text-white text-lg" />
            </div>
            <h1 class="text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
              Excel 翻译工具
            </h1>
          </div>

          <div class="flex items-center space-x-1">
            <router-link
              to="/"
              class="flex items-center space-x-2 px-4 py-2 text-blue-600 bg-blue-50 rounded-lg font-medium"
            >
              <font-awesome-icon icon="home" class="text-sm" />
              <span class="font-medium">首页</span>
            </router-link>
            <router-link
              to="/legacy"
              class="flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200"
            >
              <font-awesome-icon icon="eye" class="text-sm" />
              <span>原版界面</span>
            </router-link>
            <router-link
              to="/history"
              class="flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200"
            >
              <font-awesome-icon icon="history" class="text-sm" />
              <span class="font-medium">历史记录</span>
            </router-link>
          </div>
        </div>
      </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 pb-16">
      <!-- 步骤指示器 -->
      <div class="mb-8">
        <div class="flex items-center justify-center space-x-4 mb-6">
          <div
            v-for="(step, index) in steps"
            :key="step.id"
            class="flex items-center"
          >
            <div
              :class="[
                'flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-300',
                currentStep >= index + 1
                  ? 'bg-blue-500 border-blue-500 text-white'
                  : 'bg-white border-gray-300 text-gray-400'
              ]"
            >
              <font-awesome-icon
                v-if="currentStep > index + 1"
                icon="check"
                class="text-sm"
              />
              <font-awesome-icon
                v-else
                :icon="step.icon"
                class="text-sm"
              />
            </div>
            <div class="ml-3 hidden sm:block">
              <p
                :class="[
                  'text-sm font-medium',
                  currentStep >= index + 1 ? 'text-blue-600' : 'text-gray-500'
                ]"
              >
                {{ step.title }}
              </p>
            </div>
            <div
              v-if="index < steps.length - 1"
              :class="[
                'w-12 h-0.5 mx-4 transition-all duration-300',
                currentStep > index + 1 ? 'bg-blue-500' : 'bg-gray-300'
              ]"
            ></div>
          </div>
        </div>
      </div>

      <!-- 步骤内容 -->
      <div class="space-y-8">
        <!-- 步骤1: 文件上传 -->
        <div v-show="currentStep === 1" class="animate-fade-in">
          <FileUploadStep @file-uploaded="handleFileUploaded" :loading="loading" />
        </div>

        <!-- 步骤2: 预览确认 -->
        <div v-show="currentStep === 2" class="animate-fade-in">
          <PreviewStep
            :excel-data="excelData"
            @confirm="handlePreviewConfirm"
            @back="handleRestart"
            @restart="handleRestart"
          />
        </div>

        <!-- 步骤3: 翻译配置 -->
        <div v-show="currentStep === 3" class="animate-fade-in">
          <ConfigStep
            :languages="excelData?.languages || []"
            @start-translation="handleStartTranslation"
            @back="currentStep = 2"
          />
        </div>

        <!-- 步骤4: 翻译进度 -->
        <div v-show="currentStep === 4" class="animate-fade-in">
          <ProgressStep
            :progress="translationProgress"
            :status="translationStatus"
            @complete="handleTranslationComplete"
          />
        </div>

        <!-- 步骤5: 结果下载 -->
        <div v-show="currentStep === 5" class="animate-fade-in">
          <ResultStep
            :result-data="translationResult"
            @restart="handleRestart"
          />
        </div>

      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { ExcelData } from '@/types'
import FileUploadStep from '@/components/prototype/FileUploadStep.vue'
import PreviewStep from '@/components/prototype/PreviewStep.vue'
import ConfigStep from '@/components/prototype/ConfigStep.vue'
import ProgressStep from '@/components/prototype/ProgressStep.vue'
import ResultStep from '@/components/prototype/ResultStep.vue'

// 步骤定义
const steps = [
  { id: 1, title: '上传文件', icon: 'upload' },
  { id: 2, title: '预览确认', icon: 'eye' },
  { id: 3, title: '翻译配置', icon: 'cog' },
  { id: 4, title: '翻译进度', icon: 'spinner' },
  { id: 5, title: '下载结果', icon: 'download' }
]

// 响应式数据
const currentStep = ref(1)
const loading = ref(false)
const excelData = ref<ExcelData | null>(null)
const translationProgress = ref(0)
const translationStatus = ref<'idle' | 'processing' | 'completed' | 'error'>('idle')
const translationResult = ref<any>(null)

// 处理文件上传
const handleFileUploaded = (data: ExcelData) => {
  excelData.value = data
  currentStep.value = 2
}

// 处理预览确认 - 跳转到翻译配置
const handlePreviewConfirm = () => {
  currentStep.value = 3
}

// 处理开始翻译 - 使用假数据模拟翻译过程
const handleStartTranslation = (config: any) => {
  currentStep.value = 4
  translationStatus.value = 'processing'
  translationProgress.value = 0

  // 模拟翻译进度
  const interval = setInterval(() => {
    translationProgress.value += Math.random() * 15
    if (translationProgress.value >= 100) {
      translationProgress.value = 100
      translationStatus.value = 'completed'
      clearInterval(interval)
      setTimeout(() => {
        handleTranslationComplete()
      }, 1000)
    }
  }, 500)
}

// 处理翻译完成 - 生成假的结果数据
const handleTranslationComplete = () => {
  translationResult.value = {
    downloadUrl: '#',
    filename: `translated_${excelData.value?.metadata.filename || 'file.xlsx'}`,
    statistics: {
      totalKeys: excelData.value?.metadata.total_keys || 150,
      translatedKeys: excelData.value?.metadata.total_keys || 150,
      languages: excelData.value?.languages.length || 5
    }
  }
  currentStep.value = 5
}

// 重新开始
const handleRestart = () => {
  currentStep.value = 1
  excelData.value = null
  translationProgress.value = 0
  translationStatus.value = 'idle'
  translationResult.value = null
}
</script>

<style scoped>
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>