<template>
  <div class="h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex flex-col overflow-hidden">
    <!-- 头部导航 -->
    <header class="bg-white/80 backdrop-blur-md shadow-sm border-b border-white/20 flex-shrink-0">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center py-4">
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
              <el-icon class="text-white" :size="18">
                <Document />
              </el-icon>
            </div>
            <h1 class="text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
              翻译工具
            </h1>
          </div>
          <nav class="flex space-x-2">
            <router-link
              to="/"
              class="text-blue-600 bg-blue-50 hover:text-blue-700 hover:bg-blue-100 px-4 py-2 rounded-lg font-medium transition-all duration-200"
            >
              新版首页
            </router-link>
            <router-link
              to="/legacy"
              class="text-gray-900 hover:text-blue-600 hover:bg-blue-50 px-4 py-2 rounded-lg font-medium transition-all duration-200"
            >
              原版界面
            </router-link>
            <router-link
              to="/history"
              class="text-gray-500 hover:text-blue-600 hover:bg-blue-50 px-4 py-2 rounded-lg font-medium transition-all duration-200"
            >
              历史记录
            </router-link>
          </nav>
        </div>
      </div>
    </header>

    <div class="flex-1 flex flex-col max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 min-h-0">
      <!-- 上布局：配置和操作区域 -->
      <div class="bg-white/70 backdrop-blur-sm rounded-3xl shadow-lg border border-white/50 p-6 mb-6 flex-shrink-0">
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center space-x-3 mr-8">
            <div class="w-10 h-10 bg-gradient-to-r from-emerald-400 to-cyan-500 rounded-xl flex items-center justify-center">
              <el-icon class="text-white" :size="20">
                <Upload />
              </el-icon>
            </div>
            <h2 class="text-xl font-semibold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
              文件操作
            </h2>
          </div>
          <div class="flex space-x-3">

            <el-upload
              ref="uploadRef"
              :show-file-list="false"
              :before-upload="handleFileUpload"
              accept=".xlsx,.xls"
              :disabled="loading"
            >
              <el-button
                type="success"
                :loading="loading"
                class="shadow-md hover:shadow-lg transition-all duration-200"
                size="default"
              >
                <el-icon class="mr-2"><Upload /></el-icon>
                上传Excel文件
              </el-button>
            </el-upload>
            <el-button
              type="warning"
              @click="translateAll"
              :disabled="!hasData || loading"
              class="shadow-md hover:shadow-lg transition-all duration-200"
              size="default"
            >
              <el-icon class="mr-2"><Refresh /></el-icon>
              开始翻译
            </el-button>
          </div>
        </div>

        <!-- 文件信息 -->
        <div v-if="excelData" class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-4 border border-blue-100">
          <div class="flex items-center space-x-6 text-sm">
            <div class="flex items-center space-x-2">
              <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span class="text-gray-700 font-medium">文件:</span>
              <span class="text-blue-600 font-semibold">{{ excelData.metadata.filename }}</span>
            </div>
            <div class="flex items-center space-x-2">
              <div class="w-2 h-2 bg-green-500 rounded-full"></div>
              <span class="text-gray-700 font-medium">语言数:</span>
              <span class="text-green-600 font-semibold">{{ excelData.languages.length }}</span>
            </div>
            <div class="flex items-center space-x-2">
              <div class="w-2 h-2 bg-purple-500 rounded-full"></div>
              <span class="text-gray-700 font-medium">条目数:</span>
              <span class="text-purple-600 font-semibold">{{ excelData.metadata.total_keys }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 下布局：卡片列表区域 -->
      <div v-if="hasData" class="flex-1 flex gap-6 overflow-x-auto pb-4 scrollbar-hide min-h-0">
        <LanguageCard
          v-for="(language, index) in excelData?.languages"
          :key="language"
          :ref="(el) => setLanguageCardRef(el, index)"
          :language="language"
          :translations="getTranslationsForLanguage(language)"
          :unified-heights="unifiedItemHeights"
          @scroll-sync="handleScrollSync"
          @height-update="handleHeightUpdate"
          @update="(key, text) => handleTranslationUpdate(language, key, text)"
          :style="{ animationDelay: `${index * 100}ms` }"
          class="animate-fade-in-up"
        />
      </div>

      <!-- 空状态 -->
      <div v-else class="flex-1 flex items-center justify-center">
        <div class="text-center max-w-md">
          <div class="relative mb-8">
            <div class="w-24 h-24 bg-gradient-to-br from-blue-100 to-indigo-200 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
              <el-icon :size="40" class="text-blue-500">
                <Document />
              </el-icon>
            </div>
            <div class="absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-r from-emerald-400 to-cyan-500 rounded-full flex items-center justify-center shadow-md">
              <el-icon :size="16" class="text-white">
                <Upload />
              </el-icon>
            </div>
          </div>
          <h3 class="text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-3">
            开始您的翻译之旅
          </h3>
          <p class="text-gray-600 mb-8 leading-relaxed">
            上传Excel文件，体验高效的多语言翻译管理工具
          </p>
          <div class="flex flex-col sm:flex-row gap-3 justify-center">
            <el-upload
              ref="uploadRef"
              :show-file-list="false"
              :before-upload="handleFileUpload"
              accept=".xlsx,.xls"
              :disabled="loading"
            >
              <el-button
                type="success"
                :loading="loading"
                class="shadow-lg hover:shadow-xl transition-all duration-300"
                size="large"
              >
                <el-icon class="mr-2"><Upload /></el-icon>
                上传文件
              </el-button>
            </el-upload>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue'
import { ElButton, ElUpload, ElIcon, ElMessage } from 'element-plus'
import { Document, Upload, Refresh } from '@element-plus/icons-vue'
import LanguageCard from '@/components/LanguageCard.vue'
import { translateApiService, TranslationStyle, TranslationDomain } from '@/api/translate'
import type { ExcelData } from '@/types'

// 响应式数据
const loading = ref(false)
const excelData = ref<ExcelData | null>(null)
const uploadRef = ref()
const languageCardRefs = ref<InstanceType<typeof LanguageCard>[]>([])
const itemHeights = ref<Record<string, number>>({})

// 计算属性
const hasData = computed(() => excelData.value !== null)

// 计算每个翻译项的统一高度
const unifiedItemHeights = computed(() => {
  const heights: Record<string, number> = {}

  if (excelData.value) {
    // 为每个翻译键设置最小高度，后续会根据实际内容调整
    Object.keys(excelData.value.translations).forEach(key => {
      heights[key] = Math.max(itemHeights.value[key] || 0, 60) // 最小高度 60px，比之前更低
    })
  }

  return heights
})

// 获取指定语言的翻译数据
const getTranslationsForLanguage = (language: string) => {
  if (!excelData.value) return []

  return Object.entries(excelData.value.translations).map(([key, translations]) => ({
    key,
    text: translations[language] || ''
  }))
}

// 处理文件上传
const handleFileUpload = async (file: File) => {
  loading.value = true

  try {
    const result = await translateApiService.uploadFile(file)

    if (result.success && result.data) {
      excelData.value = result.data
      ElMessage.success('文件上传成功！')
      // 数据加载完成后重新计算高度
      nextTick(() => {
        recalculateHeights()
      })
    } else {
      ElMessage.error(result.error || '文件上传失败')
    }
  } catch (error) {
    console.error('Upload error:', error)
    ElMessage.error('文件上传失败，请重试')
  } finally {
    loading.value = false
  }

  return false // 阻止默认上传行为
}



// 翻译所有内容
const translateAll = async () => {
  if (!excelData.value) {
    ElMessage.error('请先上传文件')
    return
  }

  loading.value = true

  try {
    // 构建翻译数据
    const translationData = Object.entries(excelData.value.translations).map(([key, translations], index) => ({
      id: index + 1,
      text: key, // 使用key作为源文本
      target_languages: excelData.value!.languages
    }))

    // 构建翻译配置
    const config = {
      style: TranslationStyle.FORMAL,
      domain: TranslationDomain.GENERAL,
      preserve_formatting: true
    }

    // 启动批量翻译任务
    const result = await translateApiService.startBatchTranslation({
      data: translationData,
      config
    })

    ElMessage.success(`翻译任务已启动！任务ID: ${result.task_id}`)
    console.log('Translation task started:', result)

    // 这里可以添加任务状态轮询或跳转到历史页面查看进度

  } catch (error) {
    console.error('Translation error:', error)
    ElMessage.error('启动翻译任务失败，请重试')
  } finally {
    loading.value = false
  }
}

// 处理滚动同步
const handleScrollSync = (position: { x: number; y: number }, sourceId: string) => {
  // 同步其他卡片的滚动位置
  languageCardRefs.value.forEach((cardRef) => {
    if (cardRef && cardRef.cardId !== sourceId) {
      cardRef.syncScrollPosition(position, sourceId)
    }
  })
}

// 处理高度更新
const handleHeightUpdate = (key: string, height: number) => {
  const currentHeight = itemHeights.value[key] || 0
  if (height > currentHeight) {
    itemHeights.value[key] = height
    // 通知所有卡片更新高度
    nextTick(() => {
      languageCardRefs.value.forEach((cardRef) => {
        if (cardRef) {
          cardRef.updateItemHeight?.(key, height)
        }
      })
    })
  }
}

// 重新计算所有高度
const recalculateHeights = async () => {
  await nextTick()

  // 重置高度记录
  itemHeights.value = {}

  // 让所有卡片重新测量高度
  languageCardRefs.value.forEach((cardRef) => {
    if (cardRef) {
      cardRef.measureAllHeights?.()
    }
  })
}

// 处理翻译内容更新
const handleTranslationUpdate = (language: string, key: string, text: string) => {
  if (excelData.value && excelData.value.translations[key]) {
    excelData.value.translations[key][language] = text
  }
}

// 设置语言卡片引用
const setLanguageCardRef = (el: any, index: number) => {
  if (el) {
    languageCardRefs.value[index] = el
  }
}
</script>

<style scoped>
.grid {
  display: grid;
  gap: 1.5rem;
}
</style>